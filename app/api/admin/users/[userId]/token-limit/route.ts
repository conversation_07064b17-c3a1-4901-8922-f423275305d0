import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { TokenLimitRequest } from '@/types';

// 设置用户Token限制
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const { userId } = await params;
    const body = await request.json();
    const { tokenLimit } = body;

    // 验证输入
    if (tokenLimit !== null && (typeof tokenLimit !== 'number' || tokenLimit < 0)) {
      return NextResponse.json({
        success: false,
        message: 'Token限制必须是非负数或null（无限制）'
      }, { status: 400 });
    }

    // 检查用户是否存在
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 });
    }

    // 更新用户Token限制
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({ 
        token_limit: tokenLimit,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('更新Token限制失败:', updateError);
      return NextResponse.json({
        success: false,
        message: '更新Token限制失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: tokenLimit === null 
        ? `已移除用户 ${user.username} 的Token限制`
        : `已设置用户 ${user.username} 的Token限制为 ${tokenLimit}`
    });

  } catch (error) {
    console.error('设置Token限制失败:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 获取用户Token限制
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const { userId } = await params;

    // 获取用户Token限制
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, username, token_limit')
      .eq('id', userId)
      .single();

    if (error || !user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        username: user.username,
        tokenLimit: user.token_limit
      }
    });

  } catch (error) {
    console.error('获取Token限制失败:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
